{"name": "douyin-replay-downloader", "version": "1.0.0", "description": "抖音直播回放下载工具 JavaScript版本", "main": "main.js", "scripts": {"start": "node main.js", "build": "pkg . --out-path dist", "build-all": "pkg . --targets node18-linux-x64,node18-win-x64,node18-macos-x64 --out-path dist", "test": "node test.js"}, "keywords": ["do<PERSON><PERSON>", "download", "replay", "video", "抖音", "直播回放"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cli-progress": "^3.12.0", "commander": "^11.1.0", "readline-sync": "^1.4.10"}, "devDependencies": {"pkg": "^5.8.1"}, "pkg": {"scripts": ["main.js", "downloader.js", "worker.js", "xbogus.js", "config.js", "utils.js"], "assets": ["README.md"], "targets": ["node18-linux-x64", "node18-win-x64", "node18-macos-x64"]}, "engines": {"node": ">=16.0.0"}}