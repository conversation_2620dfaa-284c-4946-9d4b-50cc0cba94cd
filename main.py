#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音直播回放下载脚本
"""

import requests
import random
import urllib.parse
import json
import os
import subprocess
from dy_xb import <PERSON>bogus
from multiprocessing import Pool
from tqdm import tqdm
from io import BytesIO
import re
from urllib.parse import urlparse, urlunparse
from config import USER_CONFIGS, ACTIVE_USER, DOWNLOAD_CONFIG
import sys


class DouyinReplayDownloader:
    def __init__(self, user_name=None):
        # 基础URL
        self.room_info_url = "https://anchor.douyin.com/webcast/api/platform_content_player/room/v1/get_room_info"
        self.history_list_url = "https://anchor.douyin.com/webcast/data/api/v1/component/lego/native/webcast_api/room/replay/history_list"
        
        # 从配置文件读取当前启用的用户Cookie
        self.user_name = user_name or ACTIVE_USER
        if self.user_name in USER_CONFIGS:
            self.headers = {
                "x-appid": "3000",
                "x-sub-web-id": "1116",
                "cookie": USER_CONFIGS[self.user_name]['cookie']
            }
        else:
            raise ValueError(f"用户 {self.user_name} 未在配置中找到")

    # 生成随机msToken
    def get_ms_token(self):
        sequence_str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        ms_token = ''
        for i in range(0, 128):
            r = random.randint(0, len(sequence_str)-1)
            ms_token += sequence_str[r]
        return ms_token

    # 生成a_bogus参数
    def get_a_bogus(self, params):
        # 使用Xbogus类生成a_bogus参数
        xbogus = Xbogus(params)
        return xbogus.getXBogus()

    # 获取历史回放列表
    def get_history_list(self, start_date, end_date):
        # 构建参数
        params = {
            "aid": "477650",
            "device_platform": "web",
            "version_name": "10000",
            "device_type": "web",
            "startDate": start_date,
            "endDate": end_date,
            "orderField": "startTimeUnix",
            "sortType": "desc",
            "needStats": "1",
            "limit": "400",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.history_list_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        # 添加特定请求头
        headers = self.headers.copy()
        headers.update({
            "accept": "application/json, text/plain, */*",
            "content-type": "application/json",
            "webid": "1116"
        })

        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                # 解析嵌套的JSON数据
                inner_data = json.loads(data["data"]["data"])
                return inner_data["data"]["series"]
            else:
                print(f"获取历史列表失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取历史列表时发生错误: {e}")
            return None

    # 获取房间信息
    def get_room_info(self, room_id):
        # 生成参数字符串
        params = {
            "room_id": room_id,
            "is_live": "false",
            "msToken": self.get_ms_token()
        }

        # 构建参数字符串用于生成a_bogus
        param_str = "&".join([f"{k}={v}" for k, v in params.items()])

        # 生成a_bogus
        a_bogus = self.get_a_bogus(param_str)
        params["a_bogus"] = a_bogus

        # 构建完整URL
        url = self.room_info_url + "?" + "&".join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])

        try:
            response = requests.get(url, headers=self.headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"请求发生错误: {e}")
            return None

    # 获取文件切片地址的函数
    def get_ts_list(self, url):
        response = requests.get(url)
        ts_list = re.sub('#E.*', '', response.text).split()
        return ts_list

    # 获取ts切片的地址域名
    def get_base_url(self, url, just_m_domain):
        # 解析URL
        parsed_url = urlparse(url)

        # 从路径中获取目录部分
        if just_m_domain:
            directory = ''
        else:
            path_parts = parsed_url.path.split('/')
            directory = '/'.join(path_parts[:-1])

        # 重新构建URL（去除文件名）
        new_url = urlunparse(
            (parsed_url.scheme, parsed_url.netloc, directory, '', '', ''))
        return new_url

    # 下载单个切片的函数
    def download_ts(self, ts_url):
        try:
            video_data = requests.get(ts_url, timeout=30).content
            return [video_data, ts_url]
        except Exception as e:
            print(f"下载切片失败 {ts_url}: {e}")
            # 如果发生错误，返回空数据
            return [b'', ts_url]

    # 使用多进程下载视频
    def download_replay_multiprocess(self, room_id, title, start_time, output_path="./video", processes=16):
        # 获取房间信息
        room_info = self.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            print("获取房间信息失败")
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            print("未找到回放视频信息")
            return False
            
        # 获取播放链接
        m3u8_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not m3u8_url:
            print("未找到有效的视频播放链接")
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{self.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        
        try:
            print(f"开始下载视频: {m3u8_url}")
            print(f"保存路径: {file_path}.mp4")
            
            # 获取m3u8文件切片
            ts_list = self.get_ts_list(m3u8_url)
            just_domain = ts_list[0][0] == '/'
            base_url = self.get_base_url(m3u8_url, just_domain) + '/'
            ts_list = [s.lstrip('/') for s in ts_list]
            ts_url_list = [base_url + item for item in ts_list]

            print(f"总共需要下载 {len(ts_list)} 个切片")
            
            # merged_data用于临时保存下载好的切片数据
            merged_data = BytesIO()

            # 创建一个进程池，设置并行下载的进程数
            with Pool(processes) as pool:
                # 使用进程池同时下载切片文件，并保持顺序
                results = []
                for data in tqdm(pool.imap(self.download_ts, ts_url_list), total=len(ts_url_list), desc="下载进度"):
                    results.append(data)
                    merged_data.write(data[0])

            # 将文件指针移动到文件开头
            merged_data.seek(0)

            # 合并下载的切片文件
            command = ['ffmpeg', '-y', '-i', 'pipe:0', '-vcodec', 'copy', '-acodec', 'copy', f'{file_path}.mp4']
            process = subprocess.Popen(command, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print('正在进行分片合并...')
            
            _, stderr = process.communicate(input=merged_data.read())
            
            if process.returncode == 0:
                print(f"视频下载完成: {file_path}.mp4")
                return True
            else:
                print(f"视频合并失败: {stderr.decode('utf-8')}")
                return False
                
        except Exception as e:
            print(f"下载视频时发生错误: {e}")
            return False

    # 使用ffmpeg下载视频（简单方式）
    def download_replay(self, room_id, title, start_time, output_path="./video"):
        # 获取房间信息
        room_info = self.get_room_info(room_id)
        
        if not room_info or room_info.get("status_code") != 0:
            print("获取房间信息失败")
            return False
            
        # 解析播放信息
        data = room_info.get("data", {})
        play_info = data.get("play_info", [])
        
        if not play_info:
            print("未找到回放视频信息")
            return False
            
        # 获取播放链接
        video_url = play_info[0].get("hls_url") or play_info[0].get("flv_url")
        if not video_url:
            print("未找到有效的视频播放链接")
            return False
            
        # 创建输出目录
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            
        # 生成文件名（用户名-视频的时间-标题）
        # 清理标题中的非法字符
        safe_title = re.sub(r'[\\/:*?"<>|]', '_', title)
        filename = f"{self.user_name}-{start_time}-{safe_title}"
        file_path = os.path.join(output_path, filename)
        
        # 使用ffmpeg下载视频
        try:
            print(f"开始下载视频: {video_url}")
            print(f"保存路径: {file_path}.mp4")
            
            # 使用ffmpeg下载m3u8视频流
            command = ['ffmpeg', '-i', video_url, '-c', 'copy', f'{file_path}.mp4']
            result = subprocess.run(command, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if result.returncode == 0:
                print(f"视频下载完成: {file_path}.mp4")
                return True
            else:
                print(f"视频下载失败: {result.stderr.decode('utf-8')}")
                return False
        except Exception as e:
            print(f"下载视频时发生错误: {e}")
            return False

    # 批量下载指定日期范围内的所有回放视频
    def download_replays_by_date(self, start_date, end_date, output_path="./video", use_multiprocess=False, processes=16):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return False
            
        print(f"找到 {len(history_list)} 个回放视频")
        
        # 逐个下载
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"\n开始下载第 {i+1}/{len(history_list)} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            if use_multiprocess:
                success = self.download_replay_multiprocess(room_id, title, start_time, output_path, processes)
            else:
                success = self.download_replay(room_id, title, start_time, output_path)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("所有视频下载完成")
        return True

    # 列出指定日期范围内的所有回放视频，但不下载
    def list_replays_by_date(self, start_date, end_date):
        # 获取历史回放列表
        history_list = self.get_history_list(start_date, end_date)
        
        if not history_list:
            print("未找到回放视频")
            return []
            
        print(f"找到 {len(history_list)} 个回放视频:")
        print("-" * 100)
        print(f"{'序号':<4} {'房间ID':<20} {'开始时间':<20} {'标题'}")
        print("-" * 100)
        
        for i, room in enumerate(history_list):
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            print(f"{i+1:<4} {room_id:<20} {start_time:<20} {title}")
            
        print("-" * 100)
        return history_list

    # 选择并下载指定的回放视频
    def select_and_download_replays(self, history_list, selected_indices, output_path="./video", use_multiprocess=False, processes=16):
        if not history_list:
            print("没有可下载的回放视频")
            return False
            
        # 验证选择的索引
        valid_indices = [i for i in selected_indices if 1 <= i <= len(history_list)]
        if not valid_indices:
            print("没有选择有效的视频序号")
            return False
            
        print(f"开始下载 {len(valid_indices)} 个选定的视频...")
        
        # 逐个下载选定的视频
        for i in valid_indices:
            room = history_list[i-1]  # 转换为0基索引
            room_id = room['roomID']
            title = room['roomTitle']
            start_time = room['startTime']
            
            print(f"\n开始下载第 {i} 个视频:")
            print(f"房间ID: {room_id}")
            print(f"标题: {title}")
            print(f"开始时间: {start_time}")
            
            # 下载视频
            if use_multiprocess:
                success = self.download_replay_multiprocess(room_id, title, start_time, output_path, processes)
            else:
                success = self.download_replay(room_id, title, start_time, output_path)
                
            if success:
                print(f"视频下载成功: {title}")
            else:
                print(f"视频下载失败: {title}")
                
        print("选定视频下载完成")
        return True

    # 使用配置文件中的设置下载视频
    def download_replays_with_config(self, start_date=None, end_date=None):
        # 如果提供了开始日期但没有提供结束日期，则将结束日期设为与开始日期相同
        if start_date and not end_date:
            end_date = start_date
        elif not start_date:
            # 如果没有提供开始日期，则使用配置文件中的设置
            start_date = DOWNLOAD_CONFIG['start_date']
            end_date = DOWNLOAD_CONFIG['end_date']
        
        print(f"使用用户: {self.user_name}")
        print(f"开始下载 {start_date} 到 {end_date} 期间的回放视频")
        return self.download_replays_by_date(
            start_date, 
            end_date, 
            DOWNLOAD_CONFIG['output_path'], 
            DOWNLOAD_CONFIG['use_multiprocess'], 
            DOWNLOAD_CONFIG['processes']
        )


# 根据用户名创建下载器的辅助函数
def create_downloader(user_name=None):
    """根据用户名创建下载器实例"""
    return DouyinReplayDownloader(user_name)


# 显示可用用户并让用户选择
def select_user():
    """让用户选择要使用的账号"""
    users = list(USER_CONFIGS.keys())
    
    if not users:
        print("配置文件中没有找到用户配置")
        return None
    
    print("可用的用户账号:")
    print("-" * 30)
    for i, user in enumerate(users, 1):
        active_marker = " (当前默认)" if user == ACTIVE_USER else ""
        print(f"{i}. {user}{active_marker}")
    print("-" * 30)
    
    while True:
        try:
            choice = input(f"请选择用户序号 (1-{len(users)})，或按回车使用默认用户 ({ACTIVE_USER}): ").strip()
            
            if choice == "":
                return ACTIVE_USER
                
            choice_num = int(choice)
            if 1 <= choice_num <= len(users):
                selected_user = users[choice_num - 1]
                print(f"已选择用户: {selected_user}")
                return selected_user
            else:
                print(f"请输入有效的序号 (1-{len(users)})")
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            return None


if __name__ == "__main__":
    # 获取命令行参数
    start_date = None
    end_date = None
    
    if len(sys.argv) >= 2:
        start_date = sys.argv[1]
    if len(sys.argv) >= 3:
        end_date = sys.argv[2]
    
    # 让用户选择账号
    user_name = select_user()
    if user_name is None:
        print("未选择用户，程序退出")
    else:
        downloader = DouyinReplayDownloader(user_name)
        
        # 使用配置文件中的设置下载视频，同时传递可能的命令行参数
        downloader.download_replays_with_config(start_date, end_date)