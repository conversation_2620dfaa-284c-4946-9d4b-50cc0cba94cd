#!/usr/bin/python
# -*- coding: UTF-8 -*-
import time
import urllib.parse
import hashlib
import json
import binascii

class Xbogus:
    sequence_str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";#正串
    sequence = 'Dkdpgh4ZKsQB80/Mfvw36XI1R25-WUAlEi7NLboqYTOPuzmFjJnryx9HVGcaStCe=';#\w的乱串
    ubcode = 12;#{kNoMove: 2,kNoClickTouch: 4,kNoKeyboardEvent: 8,kMoveFast: 16,kKeyboardFast: 32,kFakeOperations: 64 通过 | 彼此,取值有8,12,16 不是强制参数
    ua = ''
    msToken = '';
    urlParams = {};
    
    def __init__(self,urlParams,ua=''):
        self.ua = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36' if len(ua) <=0 else ua
        self.urlParams = urlParams
        
    

    #获取ua的乱码
    def getUAMix(self):
        uf = chr(int(1/256)) + chr(int(1%256)) + chr(int(self.ubcode%256));#"\u0000\u0001\b"
        return self.mixString(uf,self.ua);#3位符


    #生成大数组MD5大数组
    #大数组是通过md5路由，之后再运算获得，从32位字符转为16位数组
    #php中等价于md5(str,true) 或者md5(hex2bin(md5(str)))
    def getArrMD(self,str):
        res = [];
        x = {'0':0,'1':1,'2':2,'3':3,'4':4,'5':5,'6':6,'7':7,'8':8,'9':9,'a':10,'b':11,'c':12,'d':13,'e':14,'f':15};
        for i in range(0,31,2):
            res.append(x[str[i]]<<4 | x[str[i+1]])
        return res;


    # 生成大数组F，最终用于产生加密串的数组
    def getArrF(self):
        # 路由参数的md5及数组
        str =  hashlib.md5(self.urlParams.encode("utf-8")).hexdigest();
        MD = hashlib.md5(binascii.unhexlify(str)).hexdigest()
        md2_arr = self.getArrMD(MD);  # 这是md5(上一个加密串,true)
        
        # 一个固定数组的md5及数组
        d41 = 'd41d8cd98f00b204e9800998ecf8427e'; #这个是固定值吗？
        dm = hashlib.md5(binascii.unhexlify(d41)).hexdigest()
        dd_arr = self.getArrMD(dm);
        
        # UA的md5及数组
        mix_ua = self.loopStr(self.getUAMix(),0)
        ua_md5 = hashlib.md5(mix_ua.encode("utf-8")).hexdigest();# ua的Md5
        ua_arr = self.getArrMD(ua_md5);# ua的乱码
        
        #print('【ua】',ua_md5,mix_ua)
        #print('【params】',str,MD,md2_arr)
        #print('【d41】',dm,dd_arr)
        
        # 初始值F
        F = [ 64,1,None,None,None,None,None,None,None,None,0.00390625,None,None,None,None,None,None,None,None]; # 其中None为变量
        #[64,1,216,69,169,100,196,186,20,55,0.00390625,12,47,63,105,85,34,42,100]
        
        timestamp = time.time()
        t = int(timestamp)
        #t = 1683342370
        
        F[3] = dd_arr[14];#d41d8cd98f00b204e9800998ecf8427e通过getArrMD后的倒数第二位，貌似是固定值,69
        F[13] = dd_arr[15];#d41d8cd98f00b204e9800998ecf8427e通过getArrMD后的最后一位，貌似是固定值,100
        F[2] = md2_arr[14];
        F[12] = md2_arr[15];
        F[11] = self.ubcode;
        F[4] = ua_arr[14];
        F[14] = ua_arr[15];
        F[5] = t >> 24 & 255;
        F[6] = t >> 8 & 255;
        F[15] = t >>16 & 255;
        F[16] = t & 255;
        F[7] = 3123319908 >> 24 & 255; #3123319908是固定值，186
        F[17] = 3123319908 >> 16 & 255; #3123319908是固定值，42
        F[8] = 3123319908 >> 8 & 255; #3123319908是固定值，20
        F[18] = 3123319908 >>0 & 255; #3123319908是固定值，100
        F[9] = self.getF9(F);#这个值是最后算的
        #print("【F】",F[9],F)
        #'A: 1924 C: 2098 j: 11 S: 22'检测最终数组
        return F;

    #获取F9，MD md5的数组
    def getF9(self,F):
        f9 = 0;
        arr = self.sortArr(F);
        for i in range(len(arr)-1):
            item = arr[i]
            f9 = int(item) ^ int(f9)
        return f9;


    #打乱数组顺序
    def sortArr(self,arr):
        sort_arr = [0,10,1,11,2,12,3,13,4,14,5,15,6,16,7,17,8,18,9]; # 对半切然后按顺序重组
        new_arr = [];
        for i in range(len(arr)):
            new_arr.append(arr[sort_arr[i]])
        return new_arr;


    #打乱F加密数组顺序
    def beforMix(self,F):
        y = '';
        sort_arr = [0,10,1,11,2,12,3,13,4,14,5,15,6,16,7,17,8,18,9]; # 对半切然后按顺序重组
        for i in range(len(sort_arr)):
            y += chr(int(F[sort_arr[i]]))
        return y;



    #获取最终19位混淆字符(最终乱码的算法)
    #不用去解他的算法，直接搬过来即可
    def mixString(self,a,b):
        e = [];
        d = 0;
        t = "" ;
        n=0;
        for f in range(0,256,1):#组装一个1-256的数组
            e.append(f)
        for r in range(0,256,1):#这里是打乱数组e的顺序
            d = (d + e[r] + ord(a[r % len(a)])) % 256;
            c = e[r];
            e[r] = e[d];
            e[d] = c;
        
        d = 0;# 重置d
        for o in range(0,len(b),1):
            n = (n + 1) % 256
            d = (d + e[n]) % 256
            c = e[n];
            e[n] = e[d];
            e[d] = c;
            t += chr(ord(b[o]) ^ e[(e[n] + e[d]) % 256])
        return t;


    '''
    *  循环获取加密字符
    *  mix_str 乱码串
    *  index 索引值，3个位一组
    *  sub_index 用于获取F的值
    *  resever 正串值还是反串值,0正串，1反串
    '''
    def getStr(self,mix_str,index,resever):
        offset = self.culOffset(mix_str,index);
        sq = self.sequence if resever else self.sequence_str # 选择正串还是乱串
        s1 = offset >> 18
        s2 = (offset & 258048)>>12
        s3 = (offset & 4032) >>6
        s4 = (offset & 63) >>0
        return sq[s1]+sq[s2]+sq[s3]+sq[s4];

    '''
    * 计算偏移量
    * mix_str 乱码串
    * index 索引值，三个为一组
    '''
    def culOffset(self,mix_str,index):
        a = ord(mix_str[index-1]);
        b = ord(mix_str[index]);
        c = ord(mix_str[index+1]);
        offset1 =  a<<16;
        offset2 =  b<<8;
        offset = offset1 + offset2 + c;
        return offset;


    #循环获取，短乱码
    '''
    * mis_str 乱码
    * sub_index 开始游标，最后加密是0，中间长乱码是从1开始
    * is_rev 是否使用反串
    '''
    def loopStr(self,mix_str,is_rev):
        final = '';
        for i in range(1,len(mix_str)+1,3):
            tmp_str = self.getStr(mix_str,i,is_rev);
            if tmp_str:
                final = final + tmp_str;
        return final;


    # 获取最终乱码串
    def getMixStr(self):
        F = self.getArrF(); #转化前的数组
        #打乱F的顺序，再获得加密串
        befor_mix = self.beforMix(F); #加密前的乱码
        after_mix = self.mixString(chr(255),befor_mix);# 混淆后乱码,"ÿ"这个是chr(255)不是字符
        final_mix = chr(2) + chr(255) + after_mix;
        return final_mix;

    # 获取最终的x-bogus
    def getXBogus(self):
        mix_str = self.getMixStr()
        return self.loopStr(mix_str,1);